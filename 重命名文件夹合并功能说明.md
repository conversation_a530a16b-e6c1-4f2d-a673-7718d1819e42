# 重命名文件夹合并功能说明

## 功能概述

在短视频预览播放器中，当重命名文件夹时如果发现同名文件夹已存在，现在提供了合并选项，允许用户将当前文件夹的内容合并到目标文件夹中。

## 新增功能

### 1. 冲突处理对话框

当重命名文件夹时遇到同名冲突，会弹出冲突处理对话框，提供以下选项：

- **合并文件夹** (绿色按钮)：将当前文件夹的所有文件移动到目标文件夹
- **重命名**：手动输入新的文件夹名称
- **自动重命名**：系统自动生成不冲突的名称（如 文件夹名-1、文件夹名-2）
- **取消**：取消重命名操作

### 2. 合并功能特点

#### 智能合并
- 将源文件夹中的所有文件移动到目标文件夹
- 自动处理文件名冲突
- 合并完成后删除空的源文件夹
- 自动切换到目标文件夹

#### 文件冲突处理
当合并过程中遇到同名文件时，提供选择：
- **是**：覆盖目标文件
- **否**：跳过当前文件
- **取消**：停止合并操作

#### 安全保护
- 合并前显示确认对话框，包含详细信息
- 显示要移动的文件数量
- 显示源文件夹和目标文件夹名称
- 提供操作结果统计

### 3. 使用场景

#### 场景1：整理重复文件夹
```
原有文件夹：
- 旅游照片/
- 旅游照片-1/

操作：将 "旅游照片-1" 重命名为 "旅游照片"
结果：两个文件夹的内容合并到 "旅游照片" 中
```

#### 场景2：合并分散内容
```
原有文件夹：
- 工作文档/
- 临时工作文档/

操作：将 "临时工作文档" 重命名为 "工作文档"
结果：所有工作文档集中到一个文件夹
```

## 操作步骤

### 1. 触发合并
1. 在当前文件夹名称上右键点击
2. 选择重命名
3. 输入已存在的文件夹名称
4. 点击确定

### 2. 选择合并
1. 在冲突对话框中点击 **"合并文件夹"** 按钮
2. 确认合并操作
3. 处理文件冲突（如有）
4. 查看合并结果

### 3. 合并确认对话框
```
将要把 X 个文件从源文件夹合并到目标文件夹。

源文件夹: [源文件夹名]
目标文件夹: [目标文件夹名]

确定要继续吗？
```

## 技术实现

### 1. 冲突检测
```python
if os.path.exists(new_folder_path):
    choice, final_name = self.show_folder_conflict_dialog(new_name, parent_dir)
```

### 2. 合并处理
```python
def merge_folders(self, source_folder, target_folder):
    # 获取源文件夹中的所有文件
    # 逐个移动文件到目标文件夹
    # 处理文件名冲突
    # 删除空的源文件夹
    # 更新界面显示
```

### 3. 自动重命名
```python
def generate_unique_folder_name(self, parent_dir, folder_name):
    # 生成 文件夹名-1, 文件夹名-2 等不冲突的名称
```

## 错误处理

### 1. 文件占用
- 自动停止视频预览
- 等待资源释放
- 提供详细错误信息

### 2. 权限问题
- 检查文件夹访问权限
- 提供权限错误提示
- 建议解决方案

### 3. 部分失败
- 统计成功和失败的文件数量
- 显示失败文件列表
- 提供重试建议

## 界面更新

### 1. 对话框设计
- 居中显示
- 清晰的选项按钮
- 绿色突出显示合并按钮
- 输入框支持键盘操作

### 2. 进度反馈
- 实时显示合并进度
- 文件移动状态提示
- 最终结果统计

### 3. 缓存管理
- 自动清理相关缓存
- 刷新文件列表
- 更新界面显示

## 注意事项

1. **备份重要数据**：合并操作不可逆，建议先备份重要文件
2. **文件占用**：确保要合并的文件没有被其他程序占用
3. **磁盘空间**：确保目标位置有足够的磁盘空间
4. **权限要求**：需要对源文件夹和目标文件夹有读写权限

## 兼容性

- 支持所有视频文件格式
- 兼容现有的重命名功能
- 不影响其他文件操作
- 向后兼容，可选择不使用合并功能

## 快捷键

- **回车键**：确认重命名
- **ESC键**：取消操作
- **Tab键**：在选项间切换
