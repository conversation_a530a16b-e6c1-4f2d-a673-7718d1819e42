# 特殊字符删除功能改进说明

## 问题描述

在使用短视频预览播放器时，遇到了删除包含特殊字符的文件和文件夹失败的问题：

1. **文件删除失败**：
   ```
   删除文件失败: V:/wangluo/t/义务教育/A19+12岁陈媛媛\IMG_20201120_215829_270.mp4, 
   错误: [WinError 5] 拒绝访问。
   ```

2. **文件夹删除失败**：
   ```
   删除文件夹失败：[WinError5]拒绝访问。：V:/wangluo/t/义务教育/Pollysets1-75andextras/P&Apaint\\IMG_6473.JPG
   ```

## 问题原因

文件名和文件夹名中包含特殊字符（如 `+`、`&` 等）时，标准的 Python 删除方法可能会失败，特别是在 Windows 系统上。

## 解决方案

### 1. 增强的文件删除函数

```python
def delete_file_with_system(file_path):
    """使用多种方法删除文件，支持特殊字符"""
```

**功能特点**：
- **多层验证**：每次删除后都验证文件是否真的被删除
- **标准方法**：首先尝试 `os.remove()` 方法
- **Windows API**：使用 `DeleteFileW` 函数处理特殊字符
- **系统命令**：使用 `del /f /q` 命令，路径用引号包围
- **PowerShell**：使用 `Remove-Item -Force` 作为最后手段
- **详细日志**：每个步骤都有详细的成功/失败日志
- **超时保护**：防止命令执行时间过长

### 2. 新增支持特殊字符的文件夹删除函数

```python
def delete_folder_with_system(folder_path):
    """使用系统方法删除文件夹，支持特殊字符"""
```

**功能特点**：
- 首先尝试标准的 `shutil.rmtree()` 方法
- 如果失败，递归逐个删除文件和子文件夹
- 对每个文件使用增强的删除方法
- 使用系统命令 `rmdir /s /q` 作为最终备选方案

### 3. 更新所有删除操作

已更新以下位置的删除操作：

1. **FileManager 类**：
   - `delete_video()` 方法
   - `delete_folder()` 方法

2. **主程序删除功能**：
   - `delete_current_file()` - 右键删除单个文件
   - `delete_selected_videos()` - 批量删除选中文件
   - `delete_unselected_videos()` - 删除未选中文件
   - `confirm_delete_folder()` - 删除整个文件夹

3. **全屏播放器**：
   - `delete_video()` - 播放器中的删除功能

## 支持的特殊字符

新的删除功能可以处理包含以下特殊字符的文件和文件夹：

- `+` (加号)
- `&` (和号)
- `%` (百分号)
- `#` (井号)
- `@` (at符号)
- `$` (美元符号)
- `()` (圆括号)
- `[]` (方括号)
- `{}` (花括号)
- 空格
- 其他 Windows 文件系统支持的特殊字符

## 错误处理

新的删除功能包含多层错误处理：

1. **标准方法**：首先尝试 Python 标准库方法
2. **系统 API**：使用 Windows API 进行删除
3. **命令行工具**：使用系统命令作为最后手段
4. **详细错误信息**：提供具体的错误原因和建议

## 使用方法

用户无需做任何改变，所有删除操作（右键删除、批量删除、文件夹删除等）都会自动使用新的增强删除功能。

## 测试

可以使用 `test_delete_special_chars.py` 脚本测试删除功能：

```bash
python test_delete_special_chars.py
```

该脚本会创建包含各种特殊字符的测试文件和文件夹，然后测试删除功能。

## 注意事项

1. 新功能主要针对 Windows 系统优化
2. 某些被系统锁定的文件仍可能无法删除
3. 建议在删除重要文件前先进行备份
4. 如果文件被其他程序占用，需要先关闭相关程序

## 兼容性

- 兼容现有的所有删除功能
- 不影响正常文件名的删除操作
- 向后兼容，不会破坏现有功能
